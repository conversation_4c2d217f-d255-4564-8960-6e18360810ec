---
import '../styles/global.css';

export interface Props {
	title: string;
	description?: string;
}

const { title, description = "Despacho legal especializado en derecho de familia, adopciones y servicios notariales en Colombia" } = Astro.props;
---

<!doctype html>
<html lang="es">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<meta name="description" content={description} />
		<title>{title}</title>

		<!-- Google Fonts -->
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Josefin+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

		<!-- Font Awesome for icons -->
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
	</head>
	<body class="bg-neutral-50 text-neutral-900" style="font-family: 'Inter', sans-serif;">
		<!-- Navigation -->
		<nav id="navbar" class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out">
			<div class="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between items-center h-20">
					<div class="flex items-center">
						<a href="/" class="flex items-center space-x-2">
							<i class="fas fa-balance-scale logo-icon text-white text-2xl transition-colors duration-300"></i>
							<span class="font-bold text-xl logo-text text-white transition-colors duration-300">LegalFamily</span>
						</a>
					</div>

					<div class="hidden md:block">
						<div class="ml-10 flex items-baseline space-x-8">
							<a href="/" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">Inicio</a>
							<a href="/servicios" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">Servicios</a>
							<a href="/nosotros" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">Nosotros</a>
							<a href="/testimonios" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">Testimonios</a>
							<a href="/faq" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">FAQ</a>
							<a href="/contacto" class="contacto-btn text-white px-4 py-2 text-sm font-medium transition-all duration-300 border" style="border-color: var(--primary-red); background-color: rgba(178, 52, 39, 0.3);">Contacto</a>
						</div>
					</div>

					<!-- Mobile menu button -->
					<div class="md:hidden">
						<button type="button" class="mobile-menu-button navbar-text focus:outline-none">
							<i class="fas fa-bars text-xl"></i>
						</button>
					</div>
				</div>
			</div>

			<!-- Mobile menu -->
			<div class="mobile-menu hidden md:hidden navbar-mobile-bg">
				<div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-white/20">
					<a href="/" class="navbar-link block px-3 py-2 text-base font-medium">Inicio</a>
					<a href="/servicios" class="navbar-link block px-3 py-2 text-base font-medium">Servicios</a>
					<a href="/nosotros" class="navbar-link block px-3 py-2 text-base font-medium">Nosotros</a>
					<a href="/testimonios" class="navbar-link block px-3 py-2 text-base font-medium">Testimonios</a>
					<a href="/faq" class="navbar-link block px-3 py-2 text-base font-medium">FAQ</a>
					<a href="/contacto" class="navbar-link block px-3 py-2 text-base font-medium">Contacto</a>
				</div>
			</div>
		</nav>

		<main>
			<slot />
		</main>

		<!-- Footer -->
		<footer class="text-white" style="background-color: #252525;">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
				<div class="grid grid-cols-1 md:grid-cols-4 gap-12">
					<div class="col-span-1 md:col-span-2">
						<div class="flex items-center space-x-3 mb-6">
							<i class="fas fa-balance-scale text-2xl" style="color: #B68C5A;"></i>
							<span class="font-bold text-xl">LegalFamily</span>
						</div>
						<p class="text-gray-300 mb-8 leading-relaxed">
							Protegemos sus derechos familiares con una asesoría legal clara, cercana y profesional. Abogados con experiencia en Colombia y en la atención a colombianos en el exterior.
						</p>
						<div class="flex space-x-6">
							<a href="#" class="text-gray-300 transition-colors" style="hover:color: #B68C5A;">
								<i class="fab fa-facebook-f text-xl"></i>
							</a>
							<a href="#" class="text-gray-300 transition-colors" style="hover:color: #B68C5A;">
								<i class="fab fa-twitter text-xl"></i>
							</a>
							<a href="#" class="text-gray-300 transition-colors" style="hover:color: #B68C5A;">
								<i class="fab fa-linkedin-in text-xl"></i>
							</a>
							<a href="#" class="text-gray-300 transition-colors" style="hover:color: #B68C5A;">
								<i class="fab fa-instagram text-xl"></i>
							</a>
						</div>
					</div>

					<div>
						<h3 class="font-medium text-lg mb-6 uppercase" style="font-family: 'Josefin Sans', sans-serif;">Servicios</h3>
						<ul class="space-y-3 text-gray-300">
							<li><a href="/servicios#familia" class="transition-colors" style="hover:color: #B23427;">Derecho de Familia</a></li>
							<li><a href="/servicios#adopciones" class="transition-colors" style="hover:color: #B23427;">Adopciones</a></li>
							<li><a href="/servicios#sucesiones" class="transition-colors" style="hover:color: #B23427;">Sucesiones</a></li>
							<li><a href="/servicios#notariales" class="transition-colors" style="hover:color: #B23427;">Servicios Notariales</a></li>
						</ul>
					</div>

					<div>
						<h3 class="font-medium text-lg mb-6 uppercase" style="font-family: 'Josefin Sans', sans-serif;">Contacto</h3>
						<ul class="space-y-4 text-gray-300">
							<li class="flex items-center space-x-3">
								<i class="fas fa-phone" style="color: var(--primary-red);"></i>
								<span>+57 (1) 234-5678</span>
							</li>
							<li class="flex items-center space-x-3">
								<i class="fas fa-envelope" style="color: var(--primary-red);"></i>
								<span><EMAIL></span>
							</li>
							<li class="flex items-center space-x-3">
								<i class="fas fa-map-marker-alt" style="color: var(--primary-red);"></i>
								<span>Bogotá, Colombia</span>
							</li>
						</ul>
					</div>
				</div>

				<div class="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
					<p class="text-gray-400 text-sm">
						© 2024 LegalFamily. Todos los derechos reservados.
					</p>
					<div class="flex space-x-8 mt-4 md:mt-0">
						<a href="/privacidad" class="text-gray-400 text-sm transition-colors" style="hover:color: var(--primary-red);">Política de Privacidad</a>
						<a href="/terminos" class="text-gray-400 text-sm transition-colors" style="hover:color: var(--primary-red);">Términos y Condiciones</a>
					</div>
				</div>
			</div>
		</footer>

		<style>
			/* CSS Custom Properties (Variables) */
			:root {
				--primary-red: #B23427;
				--primary-green: #314E52;
				--primary-dark-green: #011C1A;
				--primary-beige: #B68C5A;
				--primary-gray: #868686;
			}

			/* Navbar transparent state */
			#navbar {
				background: transparent;
				backdrop-filter: blur(10px);
				border-bottom: 1px solid rgba(255, 255, 255, 0.2);
			}

			/* Navbar scrolled state */
			#navbar.scrolled {
				background: rgba(255, 255, 255, 0.95);
				backdrop-filter: blur(10px);
				box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
				border-bottom: 1px solid rgba(0, 0, 0, 0.1);
			}

			/* Text colors for transparent state */
			.navbar-text {
				color: white;
				transition: color 0.3s ease;
			}

			.navbar-link {
				color: rgba(255, 255, 255, 0.9);
				transition: color 0.3s ease;
			}

			.navbar-link:hover {
				color: white;
			}

			.navbar-mobile-bg {
				background: rgba(0, 0, 0, 0.9);
				backdrop-filter: blur(10px);
			}

			/* Text colors for scrolled state */
			#navbar.scrolled .navbar-text {
				color: #1f2937;
			}

			/* Logo colors */
			.logo-icon {
				color: white;
			}

			.logo-text {
				color: white;
			}

			/* Logo colors when scrolled */
			#navbar.scrolled .logo-icon {
				color: var(--primary-red) !important;
			}

			#navbar.scrolled .logo-text {
				color: #1f2937 !important;
			}

			/* Contacto button styles */
			.contacto-btn {
				background-color: rgba(178, 52, 39, 0.3);
				border-color: var(--primary-red);
			}



			/* Contacto button when scrolled */
			#navbar.scrolled .contacto-btn {
				background-color: var(--primary-red) !important;
			}



			/* Footer link hover styles */
			footer a:hover {
				color: var(--primary-red) !important;
			}

			/* Form placeholder styles */
			input::placeholder,
			textarea::placeholder,
			select::placeholder {
				color: #d1d5db !important;
			}

			/* Form input text styles */
			form input[type="text"],
			form input[type="email"],
			form textarea,
			form select,
			.contact-form input,
			.contact-form textarea,
			.contact-form select {
				color: #d1d5db !important;
			}

			/* More specific form input styles */
			div input,
			div textarea,
			div select {
				color: #d1d5db !important;
			}

			#navbar.scrolled .navbar-link {
				color: #4b5563;
			}

			#navbar.scrolled .navbar-link:hover {
				color: #2563eb;
			}

			#navbar.scrolled .navbar-mobile-bg {
				background: rgba(255, 255, 255, 0.95);
			}

			#navbar.scrolled .mobile-menu {
				border-top-color: rgba(0, 0, 0, 0.1);
			}
		</style>

		<script>
			// Mobile menu toggle and scroll effect
			document.addEventListener('DOMContentLoaded', function() {
				const navbar = document.getElementById('navbar');
				const mobileMenuButton = document.querySelector('.mobile-menu-button');
				const mobileMenu = document.querySelector('.mobile-menu');

				// Mobile menu toggle
				if (mobileMenuButton && mobileMenu) {
					mobileMenuButton.addEventListener('click', function() {
						mobileMenu.classList.toggle('hidden');
					});
				}

				// Scroll effect
				function handleScroll() {
					if (window.scrollY > 50) {
						navbar.classList.add('scrolled');
					} else {
						navbar.classList.remove('scrolled');
					}
				}

				// Initial check
				handleScroll();

				// Listen for scroll events
				window.addEventListener('scroll', handleScroll);
			});
		</script>
	</body>
</html>
