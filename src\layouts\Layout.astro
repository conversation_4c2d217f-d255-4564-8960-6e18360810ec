---
import '../styles/global.css';

export interface Props {
	title: string;
	description?: string;
}

const { title, description = "Despacho legal especializado en derecho de familia, adopciones y servicios notariales en Colombia" } = Astro.props;
---

<!doctype html>
<html lang="es">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<meta name="description" content={description} />
		<title>{title}</title>

		<!-- Google Fonts -->
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Josefin+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">

		<!-- Font Awesome for icons -->
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
	</head>
	<body class="bg-neutral-50 text-neutral-900" style="font-family: 'Inter', sans-serif;">
		<!-- Navigation -->
		<nav id="navbar" class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out">
			<div class="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
				<div class="flex justify-between items-center h-20">
					<div class="flex items-center">
						<a href="/" class="flex items-center space-x-3">
							<svg class="logo-icon w-12 h-6 transition-colors duration-300" viewBox="0 0 217.36 42.99" xmlns="http://www.w3.org/2000/svg">
								<g>
									<circle class="logo-svg-fill" cx="16.79" cy="6.38" r="6.38"/>
									<circle class="logo-svg-fill" cx="28.89" cy="20.15" r="5.04"/>
									<circle class="logo-svg-fill" cx="5.04" cy="20.15" r="5.04"/>
									<rect class="logo-svg-fill" x="23.85" y="26.53" width="10.08" height="10.41"/>
									<rect class="logo-svg-fill" y="26.53" width="10.08" height="10.41"/>
									<path class="logo-svg-fill" d="m27.09,14.11H6.81c2.73,0,4.95,2.24,4.95,5.01v17.83h10.41v-18.31c.24-2.54,2.35-4.53,4.93-4.53Z"/>
									<g>
										<path class="logo-svg-fill" d="m43.33,36.95v-20.82h4.7v20.82h-4.7Zm3.69-16.79v-4.03h10.75v4.03h-10.75Zm-.34,8.4v-4.03h10.41v4.03h-10.41Z"/>
										<path class="logo-svg-fill" d="m65.86,37.18c-1.32,0-2.5-.33-3.53-.98-1.03-.65-1.85-1.54-2.45-2.66-.6-1.12-.9-2.4-.9-3.84s.3-2.72.9-3.84c.6-1.12,1.42-2.01,2.45-2.66s2.21-.98,3.53-.98c.97,0,1.84.19,2.62.56.78.37,1.41.89,1.91,1.55.49.66.77,1.41.83,2.26v6.21c-.06.85-.33,1.6-.81,2.26-.48.66-1.12,1.18-1.91,1.55-.79.37-1.67.56-2.63.56Zm.9-4.08c.97,0,1.74-.32,2.34-.96.59-.64.89-1.45.89-2.44,0-.67-.13-1.26-.4-1.77-.27-.51-.64-.91-1.12-1.2-.48-.29-1.04-.43-1.67-.43s-1.19.14-1.67.43c-.48.29-.87.68-1.15,1.2-.29.51-.43,1.1-.43,1.77s.14,1.23.41,1.74c.28.51.66.92,1.15,1.21.49.3,1.04.44,1.66.44Zm3.11,3.85v-3.89l.67-3.51-.67-3.51v-3.54h4.37v14.44h-4.37Z"/>
										<path class="logo-svg-fill" d="m77.59,36.95v-14.44h4.37v14.44h-4.37Zm9.07,0v-8.39c0-.73-.21-1.3-.64-1.7-.43-.4-.96-.61-1.58-.61-.44,0-.83.09-1.17.28-.34.19-.61.45-.81.78-.2.34-.3.75-.3,1.24l-1.68-.77c0-1.14.24-2.12.71-2.94.48-.82,1.12-1.45,1.94-1.89.82-.44,1.74-.67,2.77-.67.97,0,1.84.23,2.61.68.77.45,1.38,1.08,1.84,1.89.46.81.69,1.77.69,2.9v9.19h-4.37Zm9.07,0v-8.39c0-.73-.21-1.3-.64-1.7-.43-.4-.96-.61-1.58-.61-.44,0-.83.09-1.17.28-.34.19-.61.45-.81.78-.2.34-.3.75-.3,1.24l-2.51-.35c.04-1.22.32-2.27.84-3.15.52-.88,1.22-1.56,2.08-2.04.87-.48,1.84-.72,2.93-.72s2.01.23,2.84.69c.83.46,1.48,1.12,1.97,1.98.49.86.73,1.89.73,3.09v8.9h-4.37Z"/>
										<path class="logo-svg-fill" d="m105.5,20.54c-.71,0-1.3-.24-1.76-.72-.46-.48-.69-1.07-.69-1.76s.23-1.3.69-1.77c.46-.47,1.05-.71,1.76-.71s1.29.24,1.74.71c.45.47.68,1.06.68,1.77s-.23,1.28-.68,1.76c-.45.48-1.03.72-1.74.72Zm-2.05,16.41v-14.44h4.37v14.44h-4.37Z"/>
										<path class="logo-svg-fill" d="m111.18,36.95V15.45h4.37v21.5h-4.37Z"/>
										<path class="logo-svg-fill" d="m120.93,20.54c-.71,0-1.3-.24-1.76-.72-.46-.48-.69-1.07-.69-1.76s.23-1.3.69-1.77c.46-.47,1.05-.71,1.76-.71s1.29.24,1.74.71c.45.47.68,1.06.68,1.77s-.23,1.28-.68,1.76c-.45.48-1.03.72-1.74.72Zm-2.03,16.41v-14.44h4.37v14.44h-4.37Z"/>
										<path class="logo-svg-fill" d="m132.36,37.18c-1.32,0-2.5-.33-3.53-.98-1.03-.65-1.85-1.54-2.45-2.66-.6-1.12-.9-2.4-.9-3.84s.3-2.72.9-3.84c.6-1.12,1.42-2.01,2.45-2.66s2.21-.98,3.53-.98c.97,0,1.84.19,2.62.56.78.37,1.41.89,1.91,1.55.49.66.77,1.41.83,2.26v6.21c-.06.85-.33,1.6-.81,2.26-.48.66-1.12,1.18-1.91,1.55-.79.37-1.67.56-2.63.56Zm.93-4.08c.97,0,1.74-.32,2.34-.96.59-.64.89-1.45.89-2.44,0-.67-.13-1.26-.4-1.77-.27-.51-.64-.91-1.12-1.2-.48-.29-1.04-.43-1.67-.43s-1.19.14-1.67.43c-.48.29-.87.68-1.15,1.2-.29.51-.43,1.1-.43,1.77s.14,1.23.41,1.74c.28.51.66.92,1.15,1.21.49.3,1.04.44,1.66.44Zm3.08,3.85v-3.89l.67-3.51-.67-3.51v-3.54h4.37v14.44h-4.37Z"/>
										<path class="logo-svg-fill" d="m154,36.95l-5.92-14.44h4.89l3.75,11.42h-1.8l3.84-11.42h4.89l-6.16,14.44h-3.49Zm-3.69,6.05l4.32-9.31,2.87,3.31-2.54,6h-4.64Z"/>
										<path class="logo-svg-fill" d="m171.3,36.95v-20.82h4.7v20.82h-4.7Zm3.69,0v-4.03h10.41v4.03h-10.41Z"/>
										<path class="logo-svg-fill" d="m194.34,37.17c-1.53,0-2.89-.32-4.08-.94-1.19-.63-2.13-1.51-2.8-2.63-.68-1.12-1.02-2.39-1.02-3.81s.33-2.68,1-3.79c.67-1.11,1.57-1.99,2.72-2.63,1.14-.64,2.43-.96,3.87-.96s2.63.3,3.69.9c1.07.6,1.9,1.43,2.51,2.5.61,1.07.92,2.29.92,3.66,0,.25-.01.52-.04.8-.03.28-.08.61-.16.97l-12.18.03v-3.05l10.29-.03-1.92,1.28c-.02-.81-.15-1.49-.38-2.02-.23-.53-.58-.94-1.03-1.22-.46-.28-1.01-.42-1.67-.42-.7,0-1.3.16-1.82.48-.51.32-.91.77-1.19,1.35-.28.58-.42,1.29-.42,2.12s.15,1.55.45,2.14c.3.59.73,1.05,1.28,1.37s1.21.48,1.96.48c.7,0,1.33-.12,1.89-.36.56-.24,1.06-.61,1.48-1.09l2.44,2.44c-.7.81-1.54,1.42-2.53,1.83-.99.41-2.07.61-3.26.61Z"/>
										<path class="logo-svg-fill" d="m207.71,36.95l-5.92-14.44h4.89l3.75,11.42h-1.8l3.84-11.42h4.89l-6.16,14.44h-3.49Zm-3.69,6.05l4.32-9.31,2.87,3.31-2.54,6h-4.64Z"/>
									</g>
								</g>
							</svg>
			
						</a>
					</div>

					<div class="hidden md:block">
						<div class="ml-10 flex items-baseline space-x-8">
							<a href="/" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">Inicio</a>
							<div class="relative group">
								<button class="navbar-link px-3 py-2 text-sm font-medium transition-colors flex items-center">
									Servicios
									<i class="fas fa-chevron-down ml-1 text-xs transition-transform group-hover:rotate-180"></i>
								</button>
								<div class="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
									<div class="py-2 desktop-services-submenu">
										<a href="/servicios/divorcios-liquidaciones" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Divorcios Y Liquidaciones De Sociedad Conyugal Y Patrimonial</a>
										<a href="/servicios/custodia-menores" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Custodia de Infantes</a>
										<a href="/servicios/cuota-alimentaria-visitas" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Regulación De Cuota Alimentaria Y De Visitas</a>
										<a href="/servicios/permisos-salida-pais" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Permisos De Salida Del País De Menores</a>
										<a href="/servicios/adopciones-en-colombia" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Adopciones</a>
										<a href="/servicios/sucesiones-herencias" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Sucesiones Y Herencias</a>
										<a href="/servicios/violencia-intrafamiliar" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Violencia Intrafamiliar</a>
										<a href="/servicios/representacion-comisarias" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Representación Ante Comisarías De Familia Y Juzgados</a>
										<a href="/servicios/patrimonios-familia" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Patrimonios De Familia</a>
										<a href="/servicios/consultoria-especializada" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Consultoría Especializada</a>
										<a href="/servicios/consultoria-especializada" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Asesorías en trámites notariales</a>
										<a href="/servicios/consultoria-especializada" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors">Asesorías en trámites registrales</a>
									</div>
								</div>
							</div>
							<a href="/nosotros" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">Nosotros</a>
							<a href="/guias" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">Guías</a>
							<a href="/preguntas-frecuentes" class="navbar-link px-3 py-2 text-sm font-medium transition-colors">Preguntas frecuentes</a>
							<a href="/contacto" class="contacto-btn text-white px-4 py-2 text-sm font-medium transition-all duration-300 border" style="border-color: var(--primary-red); background-color: rgba(178, 52, 39, 0.3);">Contacto</a>
						</div>
					</div>

					<!-- Mobile menu button -->
					<div class="md:hidden">
						<button type="button" class="mobile-menu-button navbar-text focus:outline-none">
							<i class="fas fa-bars text-xl"></i>
						</button>
					</div>
				</div>
			</div>

			<!-- Mobile menu -->
			<div class="mobile-menu hidden md:hidden navbar-mobile-bg">
				<div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-white/20">
					<a href="/" class="navbar-link block px-3 py-2 text-base font-medium">Inicio</a>
					<div class="relative">
						<button class="services-menu-button navbar-link w-full text-left block px-3 py-2 text-base font-medium flex justify-between items-center">
							<span>Servicios</span>
							<i class="fas fa-chevron-down ml-1 text-xs transition-transform"></i>
						</button>
						<div class="services-submenu hidden mt-2 space-y-1 pl-4">
							<a href="/servicios/divorcios-liquidaciones" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Divorcios Y Liquidaciones</a>
							<a href="/servicios/custodia-de-infantes" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Custodia de Infantes</a>
							<a href="/servicios/regulacion-cuota-alimentaria-visitas" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Regulación De Cuota Alimentaria Y Visitas</a>
							<a href="/servicios/permisos-salida-del-pais-menores" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Permisos De Salida Del País De Menores</a>
							<a href="/servicios/adopciones-en-colombia" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Adopciones</a>
							<a href="/servicios/sucesiones-y-herencias" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Sucesiones Y Herencias</a>
							<a href="/servicios/violencia-intrafamiliar" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Violencia Intrafamiliar</a>
							<a href="/servicios/representacion-ante-comisarias-y-juzgados" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Representación Ante Comisarías Y Juzgados</a>
							<a href="/servicios/patrimonios-de-familia" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Patrimonios De Familia</a>
							<a href="/servicios/consultoria-especializada" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Consultoría Especializada</a>
							<a href="/servicios/tramites-notariales" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Asesorías en trámites notariales</a>
							<a href="/servicios/tramites-registrales" class="block px-3 py-2 text-sm text-gray-300 hover:text-white">Asesorías en trámites registrales</a>
						</div>
					</div>
					<a href="/nosotros" class="navbar-link block px-3 py-2 text-base font-medium">Nosotros</a>
					<a href="/guias" class="navbar-link block px-3 py-2 text-base font-medium">Guías</a>
					<a href="/preguntas-frecuentes" class="navbar-link block px-3 py-2 text-base font-medium">Preguntas frecuentes</a>
					<a href="/contacto" class="navbar-link block px-3 py-2 text-base font-medium">Contacto</a>
				</div>
			</div>
		</nav>

		<main>
			<slot />
		</main>

		<!-- Footer -->
		<footer class="text-white" style="background-color: #1c232c;">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
				<div class="grid grid-cols-1 md:grid-cols-4 gap-12">
					<div class="col-span-1 md:col-span-2">
						<div class="flex items-center space-x-3 mb-6">
							<svg class="logo-icon-bottom w-12 h-6" viewBox="0 0 217.36 42.99" xmlns="http://www.w3.org/2000/svg">
								<g>
									<circle class="logo-svg-fill" cx="16.79" cy="6.38" r="6.38"/>
									<circle class="logo-svg-fill" cx="28.89" cy="20.15" r="5.04"/>
									<circle class="logo-svg-fill" cx="5.04" cy="20.15" r="5.04"/>
									<rect class="logo-svg-fill" x="23.85" y="26.53" width="10.08" height="10.41"/>
									<rect class="logo-svg-fill" y="26.53" width="10.08" height="10.41"/>
									<path class="logo-svg-fill" d="m27.09,14.11H6.81c2.73,0,4.95,2.24,4.95,5.01v17.83h10.41v-18.31c.24-2.54,2.35-4.53,4.93-4.53Z"/>
									<g>
										<path class="logo-svg-fill" d="m43.33,36.95v-20.82h4.7v20.82h-4.7Zm3.69-16.79v-4.03h10.75v4.03h-10.75Zm-.34,8.4v-4.03h10.41v4.03h-10.41Z"/>
										<path class="logo-svg-fill" d="m65.86,37.18c-1.32,0-2.5-.33-3.53-.98-1.03-.65-1.85-1.54-2.45-2.66-.6-1.12-.9-2.4-.9-3.84s.3-2.72.9-3.84c.6-1.12,1.42-2.01,2.45-2.66s2.21-.98,3.53-.98c.97,0,1.84.19,2.62.56.78.37,1.41.89,1.91,1.55.49.66.77,1.41.83,2.26v6.21c-.06.85-.33,1.6-.81,2.26-.48.66-1.12,1.18-1.91,1.55-.79.37-1.67.56-2.63.56Zm.9-4.08c.97,0,1.74-.32,2.34-.96.59-.64.89-1.45.89-2.44,0-.67-.13-1.26-.4-1.77-.27-.51-.64-.91-1.12-1.2-.48-.29-1.04-.43-1.67-.43s-1.19.14-1.67.43c-.48.29-.87.68-1.15,1.2-.29.51-.43,1.1-.43,1.77s.14,1.23.41,1.74c.28.51.66.92,1.15,1.21.49.3,1.04.44,1.66.44Zm3.11,3.85v-3.89l.67-3.51-.67-3.51v-3.54h4.37v14.44h-4.37Z"/>
										<path class="logo-svg-fill" d="m77.59,36.95v-14.44h4.37v14.44h-4.37Zm9.07,0v-8.39c0-.73-.21-1.3-.64-1.7-.43-.4-.96-.61-1.58-.61-.44,0-.83.09-1.17.28-.34.19-.61.45-.81.78-.2.34-.3.75-.3,1.24l-1.68-.77c0-1.14.24-2.12.71-2.94.48-.82,1.12-1.45,1.94-1.89.82-.44,1.74-.67,2.77-.67.97,0,1.84.23,2.61.68.77.45,1.38,1.08,1.84,1.89.46.81.69,1.77.69,2.9v9.19h-4.37Zm9.07,0v-8.39c0-.73-.21-1.3-.64-1.7-.43-.4-.96-.61-1.58-.61-.44,0-.83.09-1.17.28-.34.19-.61.45-.81.78-.2.34-.3.75-.3,1.24l-2.51-.35c.04-1.22.32-2.27.84-3.15.52-.88,1.22-1.56,2.08-2.04.87-.48,1.84-.72,2.93-.72s2.01.23,2.84.69c.83.46,1.48,1.12,1.97,1.98.49.86.73,1.89.73,3.09v8.9h-4.37Z"/>
										<path class="logo-svg-fill" d="m105.5,20.54c-.71,0-1.3-.24-1.76-.72-.46-.48-.69-1.07-.69-1.76s.23-1.3.69-1.77c.46-.47,1.05-.71,1.76-.71s1.29.24,1.74.71c.45.47.68,1.06.68,1.77s-.23,1.28-.68,1.76c-.45.48-1.03.72-1.74.72Zm-2.05,16.41v-14.44h4.37v14.44h-4.37Z"/>
										<path class="logo-svg-fill" d="m111.18,36.95V15.45h4.37v21.5h-4.37Z"/>
										<path class="logo-svg-fill" d="m120.93,20.54c-.71,0-1.3-.24-1.76-.72-.46-.48-.69-1.07-.69-1.76s.23-1.3.69-1.77c.46-.47,1.05-.71,1.76-.71s1.29.24,1.74.71c.45.47.68,1.06.68,1.77s-.23,1.28-.68,1.76c-.45.48-1.03.72-1.74.72Zm-2.03,16.41v-14.44h4.37v14.44h-4.37Z"/>
										<path class="logo-svg-fill" d="m132.36,37.18c-1.32,0-2.5-.33-3.53-.98-1.03-.65-1.85-1.54-2.45-2.66-.6-1.12-.9-2.4-.9-3.84s.3-2.72.9-3.84c.6-1.12,1.42-2.01,2.45-2.66s2.21-.98,3.53-.98c.97,0,1.84.19,2.62.56.78.37,1.41.89,1.91,1.55.49.66.77,1.41.83,2.26v6.21c-.06.85-.33,1.6-.81,2.26-.48.66-1.12,1.18-1.91,1.55-.79.37-1.67.56-2.63.56Zm.93-4.08c.97,0,1.74-.32,2.34-.96.59-.64.89-1.45.89-2.44,0-.67-.13-1.26-.4-1.77-.27-.51-.64-.91-1.12-1.2-.48-.29-1.04-.43-1.67-.43s-1.19.14-1.67.43c-.48.29-.87.68-1.15,1.2-.29.51-.43,1.1-.43,1.77s.14,1.23.41,1.74c.28.51.66.92,1.15,1.21.49.3,1.04.44,1.66.44Zm3.08,3.85v-3.89l.67-3.51-.67-3.51v-3.54h4.37v14.44h-4.37Z"/>
										<path class="logo-svg-fill" d="m154,36.95l-5.92-14.44h4.89l3.75,11.42h-1.8l3.84-11.42h4.89l-6.16,14.44h-3.49Zm-3.69,6.05l4.32-9.31,2.87,3.31-2.54,6h-4.64Z"/>
										<path class="logo-svg-fill" d="m171.3,36.95v-20.82h4.7v20.82h-4.7Zm3.69,0v-4.03h10.41v4.03h-10.41Z"/>
										<path class="logo-svg-fill" d="m194.34,37.17c-1.53,0-2.89-.32-4.08-.94-1.19-.63-2.13-1.51-2.8-2.63-.68-1.12-1.02-2.39-1.02-3.81s.33-2.68,1-3.79c.67-1.11,1.57-1.99,2.72-2.63,1.14-.64,2.43-.96,3.87-.96s2.63.3,3.69.9c1.07.6,1.9,1.43,2.51,2.5.61,1.07.92,2.29.92,3.66,0,.25-.01.52-.04.8-.03.28-.08.61-.16.97l-12.18.03v-3.05l10.29-.03-1.92,1.28c-.02-.81-.15-1.49-.38-2.02-.23-.53-.58-.94-1.03-1.22-.46-.28-1.01-.42-1.67-.42-.7,0-1.3.16-1.82.48-.51.32-.91.77-1.19,1.35-.28.58-.42,1.29-.42,2.12s.15,1.55.45,2.14c.3.59.73,1.05,1.28,1.37s1.21.48,1.96.48c.7,0,1.33-.12,1.89-.36.56-.24,1.06-.61,1.48-1.09l2.44,2.44c-.7.81-1.54,1.42-2.53,1.83-.99.41-2.07.61-3.26.61Z"/>
										<path class="logo-svg-fill" d="m207.71,36.95l-5.92-14.44h4.89l3.75,11.42h-1.8l3.84-11.42h4.89l-6.16,14.44h-3.49Zm-3.69,6.05l4.32-9.31,2.87,3.31-2.54,6h-4.64Z"/>
									</g>
								</g>
							</svg>
							
						</div>
						<p class="text-gray-300 mb-8 leading-relaxed">
							Protegemos sus derechos familiares con una asesoría legal clara, cercana y profesional. Abogados con experiencia en Colombia y en la atención a colombianos en el exterior.
						</p>
						<div class="flex space-x-6">
							<a href="#" class="text-gray-300 transition-colors" style="hover:color: #B68C5A;">
								<i class="fab fa-facebook-f text-xl"></i>
							</a>
							<a href="#" class="text-gray-300 transition-colors" style="hover:color: #B68C5A;">
								<i class="fab fa-twitter text-xl"></i>
							</a>
							<a href="#" class="text-gray-300 transition-colors" style="hover:color: #B68C5A;">
								<i class="fab fa-linkedin-in text-xl"></i>
							</a>
							<a href="#" class="text-gray-300 transition-colors" style="hover:color: #B68C5A;">
								<i class="fab fa-instagram text-xl"></i>
							</a>
						</div>
					</div>

					<div>
						<h3 class="font-medium text-lg mb-6 uppercase" style="font-family: 'Josefin Sans', sans-serif;">Servicios</h3>
						<ul class="space-y-3 text-gray-300">
							<li><a href="/servicios#familia" class="transition-colors" style="hover:color: #B23427;">Derecho de Familia</a></li>
							<li><a href="/servicios#adopciones" class="transition-colors" style="hover:color: #B23427;">Adopciones</a></li>
							<li><a href="/servicios#sucesiones" class="transition-colors" style="hover:color: #B23427;">Sucesiones</a></li>
							<li><a href="/servicios#notariales" class="transition-colors" style="hover:color: #B23427;">Servicios Notariales</a></li>
						</ul>
					</div>

					<div>
						<h3 class="font-medium text-lg mb-6 uppercase" style="font-family: 'Josefin Sans', sans-serif;">Contacto</h3>
						<ul class="space-y-4 text-gray-300">
							<li class="flex items-center space-x-3">
								<i class="fas fa-phone" style="color: var(--primary-red);"></i>
								<span>+57 (1) 234-5678</span>
							</li>
							<li class="flex items-center space-x-3">
								<i class="fas fa-envelope" style="color: var(--primary-red);"></i>
								<span><EMAIL></span>
							</li>
							<li class="flex items-center space-x-3">
								<i class="fas fa-map-marker-alt" style="color: var(--primary-red);"></i>
								<span>Bogotá, Colombia</span>
							</li>
						</ul>
					</div>
				</div>

				<div class="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
					<p class="text-gray-400 text-sm">
						© 2024 LegalFamily. Todos los derechos reservados.
					</p>
					<div class="flex space-x-8 mt-4 md:mt-0">
						<a href="/privacidad" class="text-gray-400 text-sm transition-colors" style="hover:color: var(--primary-red);">Política de Privacidad</a>
						<a href="/terminos" class="text-gray-400 text-sm transition-colors" style="hover:color: var(--primary-red);">Términos y Condiciones</a>
					</div>
				</div>
			</div>
		</footer>

		<style>
			/* CSS Custom Properties (Variables) */
			:root {
				--primary-red: #B23427;
				--primary-green: #314E52;
				--primary-dark-green: #011C1A;
				--primary-beige: #B68C5A;
				--primary-gray: #868686;
			}

			/* Navbar transparent state */
			#navbar {
				background: transparent;
				backdrop-filter: blur(10px);
				border-bottom: 1px solid rgba(255, 255, 255, 0.2);
			}

			/* Navbar scrolled state */
			#navbar.scrolled {
				background: rgba(255, 255, 255, 0.95);
				backdrop-filter: blur(10px);
				box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
				border-bottom: 1px solid rgba(0, 0, 0, 0.1);
			}

			/* Text colors for transparent state */
			.navbar-text {
				color: white;
				transition: color 0.3s ease;
			}

			.navbar-link {
				color: rgba(255, 255, 255, 0.9);
				transition: color 0.3s ease;
			}

			.navbar-link:hover {
				color: white;
			}

			.navbar-mobile-bg {
				background: rgba(0, 0, 0, 0.9);
				backdrop-filter: blur(10px);
			}

			/* Text colors for scrolled state */
			#navbar.scrolled .navbar-text {
				color: #1f2937;
			}

			/* Logo colors */
			.logo-icon {
				color: white;
				width: 179px;
    			height: 36px;
			}

			.logo-text {
				color: white;
			}

			/* Logo SVG fill colors */
			.logo-svg-fill {
				fill: white;
				transition: fill 0.3s ease;
			}
			.logo-icon-bottom{
				width: 220px;
				height: 100px;
			}
			/* Logo colors when scrolled */
			#navbar.scrolled .logo-icon {
				color: var(--primary-red) !important;
			}

			#navbar.scrolled .logo-text {
				color: #1f2937 !important;
			}

			/* Logo SVG colors when scrolled */
			#navbar.scrolled .logo-svg-fill {
				fill: var(--primary-dark-green) !important;
			}

			/* Contacto button styles */
			.contacto-btn {
				background-color: rgba(178, 52, 39, 0.3);
				border-color: var(--primary-red);
			}



			/* Contacto button when scrolled */
			#navbar.scrolled .contacto-btn {
				background-color: var(--primary-red) !important;
			}



			/* Footer link hover styles */
			footer a:hover {
				color: var(--primary-red) !important;
			}

			/* Form placeholder styles */
			input::placeholder,
			textarea::placeholder,
			select::placeholder {
				color: #d1d5db !important;
			}

			/* Form input text styles */
			form input[type="text"],
			form input[type="email"],
			form textarea,
			form select,
			.contact-form input,
			.contact-form textarea,
			.contact-form select {
				color: #d1d5db !important;
			}

			/* More specific form input styles */
			div input,
			div textarea,
			div select {
				color: #d1d5db !important;
			}

			#navbar.scrolled .navbar-link {
				color: #4b5563;
			}

			#navbar.scrolled .navbar-link:hover {
				color: #2563eb;
			}

			#navbar.scrolled .navbar-mobile-bg {
				background: rgba(255, 255, 255, 0.95);
			}

			#navbar.scrolled .mobile-menu {
				border-top-color: rgba(0, 0, 0, 0.1);
			}

			/* Dropdown menu styles */
			.group:hover .group-hover\:opacity-100 {
				opacity: 1;
			}

			.group:hover .group-hover\:visible {
				visibility: visible;
			}

			.group:hover .group-hover\:rotate-180 {
				transform: rotate(180deg);
			}

			/* Dropdown menu scrolled state */
			#navbar.scrolled .group button {
				color: #4b5563;
			}

			#navbar.scrolled .group:hover button {
				color: #2563eb;
			}
.services-submenu a.active,
    .desktop-services-submenu a.active {
        background: #9696a6;
        color: white;
        font-weight: 700;
    }
		</style>

		<script>
			// Mobile menu toggle and scroll effect
			document.addEventListener('DOMContentLoaded', function() {
				const navbar = document.getElementById('navbar');
				const mobileMenuButton = document.querySelector('.mobile-menu-button');
				const mobileMenu = document.querySelector('.mobile-menu');
				const servicesMenuButton = document.querySelector('.services-menu-button');
				const servicesSubmenu = document.querySelector('.services-submenu');

				// Mobile menu toggle
				if (mobileMenuButton && mobileMenu) {
					mobileMenuButton.addEventListener('click', function() {
						mobileMenu.classList.toggle('hidden');
					});
				}

				// Services submenu toggle
				if (servicesMenuButton && servicesSubmenu) {
					servicesMenuButton.addEventListener('click', function(e) {
						e.preventDefault();
						servicesSubmenu.classList.toggle('hidden');
						const icon = servicesMenuButton.querySelector('i');
						icon.classList.toggle('rotate-180');
					});
				}

				// Scroll effect
				function handleScroll() {
					if (window.scrollY > 50) {
						navbar.classList.add('scrolled');
					} else {
						navbar.classList.remove('scrolled');
					}
				}

				// Initial check
				handleScroll();

				// Listen for scroll events
				window.addEventListener('scroll', handleScroll);
 // Active link highlighting
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.services-submenu a, .desktop-services-submenu a');

    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
			});
		</script>
	</body>
</html>
