---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Contacto - Familia y Ley Abogados">
	<!-- Hero Section -->
	<section class="relative h-[40vh] lg:h-[50vh] flex items-center bg-cover bg-center bg-no-repeat" style="background-image: url('/images/hero-bg2.jpg');">
		<!-- Black Overlay -->
		<div class="absolute inset-0" style="background-color: rgba(0, 0, 0, 0.6);"></div>
		<div class="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 w-full relative z-10">
			<div class="text-center">
				<h1 class="text-4xl sm:text-5xl lg:text-6xl font-medium text-white mb-4 leading-tight uppercase" style="font-family: 'Josefin Sans', sans-serif;">
					Contáctanos
				</h1>
				<p class="text-lg lg:text-xl text-white/90 max-w-4xl mx-auto leading-relaxed">
					Estamos aquí para ayudarte con tu consulta legal especializada
				</p>
			</div>
		</div>
	</section>

	<!-- Contact Section -->
	<section class="py-16 lg:py-24 bg-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
				<!-- Contact Form -->
				<div>
					<h2 class="text-3xl sm:text-4xl lg:text-5xl font-medium mb-6 uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
						Solicita tu Consulta
					</h2>
					<p class="text-lg leading-relaxed mb-8" style="color: #4A4A4A;">
						<strong>Para mayor información completa este formulario, te contactaremos lo antes posible.</strong><br>
						Asegúrate de insertar un email correcto, de lo contrario será imposible para nosotros responder.
					</p>

					<form class="space-y-6 contact-form">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label for="nombre" class="block text-sm font-medium mb-2" style="color: #314E52;">
									Nombre *
								</label>
								<input 
									type="text" 
									id="nombre" 
									name="nombre" 
									required 
									class="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
									style="background-color: rgba(255, 255, 255, 0.9);"
								>
							</div>
							<div>
								<label for="apellido" class="block text-sm font-medium mb-2" style="color: #314E52;">
									Apellido *
								</label>
								<input 
									type="text" 
									id="apellido" 
									name="apellido" 
									required 
									class="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
									style="background-color: rgba(255, 255, 255, 0.9);"
								>
							</div>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label for="telefono" class="block text-sm font-medium mb-2" style="color: #314E52;">
									Teléfono *
								</label>
								<input 
									type="tel" 
									id="telefono" 
									name="telefono" 
									required 
									class="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
									style="background-color: rgba(255, 255, 255, 0.9);"
								>
							</div>
							<div>
								<label for="email" class="block text-sm font-medium mb-2" style="color: #314E52;">
									Email *
								</label>
								<input 
									type="email" 
									id="email" 
									name="email" 
									required 
									class="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
									style="background-color: rgba(255, 255, 255, 0.9);"
								>
							</div>
						</div>

						<div>
							<label for="servicio" class="block text-sm font-medium mb-2" style="color: #314E52;">
								Tipo de Consulta
							</label>
							<select 
								id="servicio" 
								name="servicio" 
								class="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors"
								style="background-color: rgba(255, 255, 255, 0.9);"
							>
								<option value="">Selecciona un servicio</option>
								<option value="divorcio">Divorcio y Liquidación de Sociedad Conyugal</option>
								<option value="custodia">Custodia de Menores</option>
								<option value="alimentos">Regulación de Cuota Alimentaria</option>
								<option value="adopciones">Adopciones</option>
								<option value="sucesiones">Sucesiones y Herencias</option>
								<option value="violencia">Violencia Intrafamiliar</option>
								<option value="civil">Derecho Civil</option>
								<option value="otro">Otro</option>
							</select>
						</div>

						<div>
							<label for="mensaje" class="block text-sm font-medium mb-2" style="color: #314E52;">
								Describe tu consulta *
							</label>
							<textarea 
								id="mensaje" 
								name="mensaje" 
								rows="6" 
								required 
								placeholder="Describe brevemente tu situación legal..."
								class="w-full px-4 py-3 border border-gray-300 focus:ring-2 focus:ring-red-500 focus:border-transparent transition-colors resize-vertical"
								style="background-color: rgba(255, 255, 255, 0.9);"
							></textarea>
						</div>

						<div class="flex items-start space-x-3">
							<input 
								type="checkbox" 
								id="terminos" 
								name="terminos" 
								required 
								class="mt-1 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
							>
							<label for="terminos" class="text-sm" style="color: #4A4A4A;">
								Acepto los <a href="/terminos" class="underline" style="color: var(--primary-red);">términos y condiciones</a> y la <a href="/privacidad" class="underline" style="color: var(--primary-red);">política de privacidad</a>
							</label>
						</div>

						<button 
							type="submit" 
							class="w-full md:w-auto px-8 py-4 text-lg font-medium text-white transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
							style="background-color: var(--primary-red);"
						>
							<i class="fas fa-paper-plane mr-3"></i>
							Enviar Consulta
						</button>
					</form>
				</div>

				<!-- Contact Information -->
				<div>
					<div class="mb-8">
						<h3 class="text-2xl lg:text-3xl font-medium mb-6 uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
							Información de Contacto
						</h3>
						
						<!-- Lawyer Photo Placeholder -->
						<div class="mb-8 text-center">
							<div class="w-48 h-64 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
								<div class="text-center text-gray-500">
									<i class="fas fa-user text-6xl mb-4"></i>
									<p class="text-lg">Dr. Jorge Enrique Calixto</p>
									<p class="text-sm">[Foto del Abogado]</p>
								</div>
							</div>
						</div>

						<div class="space-y-6">
							<div class="flex items-start space-x-4">
								<div class="flex-shrink-0 mt-1">
									<div class="w-12 h-12 rounded-full flex items-center justify-center" style="background-color: var(--primary-red);">
										<i class="fas fa-phone text-white text-lg"></i>
									</div>
								</div>
								<div>
									<h4 class="text-lg font-semibold mb-2" style="color: #314E52;">Teléfonos</h4>
									<p class="text-lg" style="color: #4A4A4A;">
										<a href="tel:+5713001234567" class="hover:underline">+57 (1) ************</a><br>
										<a href="tel:+5713001234568" class="hover:underline">+57 (1) ************</a>
									</p>
								</div>
							</div>

							<div class="flex items-start space-x-4">
								<div class="flex-shrink-0 mt-1">
									<div class="w-12 h-12 rounded-full flex items-center justify-center" style="background-color: var(--primary-red);">
										<i class="fas fa-envelope text-white text-lg"></i>
									</div>
								</div>
								<div>
									<h4 class="text-lg font-semibold mb-2" style="color: #314E52;">Email</h4>
									<p class="text-lg" style="color: #4A4A4A;">
										<a href="mailto:<EMAIL>" class="hover:underline"><EMAIL></a>
									</p>
								</div>
							</div>

							<div class="flex items-start space-x-4">
								<div class="flex-shrink-0 mt-1">
									<div class="w-12 h-12 rounded-full flex items-center justify-center" style="background-color: var(--primary-red);">
										<i class="fas fa-clock text-white text-lg"></i>
									</div>
								</div>
								<div>
									<h4 class="text-lg font-semibold mb-2" style="color: #314E52;">Horarios de Atención</h4>
									<p class="text-lg" style="color: #4A4A4A;">
										Lunes a Viernes<br>
										9:00 AM - 6:00 PM
									</p>
								</div>
							</div>

							<div class="flex items-start space-x-4">
								<div class="flex-shrink-0 mt-1">
									<div class="w-12 h-12 rounded-full flex items-center justify-center" style="background-color: var(--primary-red);">
										<i class="fas fa-map-marker-alt text-white text-lg"></i>
									</div>
								</div>
								<div>
									<h4 class="text-lg font-semibold mb-2" style="color: #314E52;">Dirección</h4>
									<p class="text-lg" style="color: #4A4A4A;">
										Carrera 15 #93-47, Oficina 501<br>
										Bogotá, Colombia
									</p>
								</div>
							</div>

							<div class="flex items-start space-x-4">
								<div class="flex-shrink-0 mt-1">
									<div class="w-12 h-12 rounded-full flex items-center justify-center" style="background-color: var(--primary-red);">
										<i class="fas fa-globe text-white text-lg"></i>
									</div>
								</div>
								<div>
									<h4 class="text-lg font-semibold mb-2" style="color: #314E52;">Atención Internacional</h4>
									<p class="text-lg" style="color: #4A4A4A;">
										Asesoramos a colombianos<br>
										en el exterior
									</p>
								</div>
							</div>
						</div>
					</div>

					<!-- Emergency Contact -->
					<div class="p-6 rounded-lg" style="background-color: #F5F5F5;">
						<h4 class="text-xl font-semibold mb-4 uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
							Consulta de Emergencia
						</h4>
						<p class="text-lg leading-relaxed mb-4" style="color: #4A4A4A;">
							Para casos urgentes que requieren atención inmediata, contáctanos a través de WhatsApp.
						</p>
						<a 
							href="https://wa.me/573001234567" 
							target="_blank"
							class="inline-flex items-center px-6 py-3 text-white font-medium transition-all duration-300 hover:shadow-lg"
							style="background-color: #25D366;"
						>
							<i class="fab fa-whatsapp mr-3 text-xl"></i>
							WhatsApp
						</a>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Additional Services Section -->
	<section class="py-16 lg:py-20 bg-gray-50">
		<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-12">
				<h3 class="text-2xl lg:text-3xl font-medium mb-4 uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
					¿Cómo podemos ayudarte?
				</h3>
				<p class="text-lg" style="color: #868686;">
					Nuestros servicios especializados en derecho de familia
				</p>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
				<div class="text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
					<div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style="background-color: var(--primary-red);">
						<i class="fas fa-gavel text-white text-2xl"></i>
					</div>
					<h4 class="text-lg font-semibold mb-2 uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
						Divorcios
					</h4>
					<p class="text-base" style="color: #4A4A4A;">
						Asesoría integral en procesos de divorcio y liquidación de sociedad conyugal
					</p>
				</div>

				<div class="text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
					<div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style="background-color: var(--primary-red);">
						<i class="fas fa-heart text-white text-2xl"></i>
					</div>
					<h4 class="text-lg font-semibold mb-2 uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
						Adopciones
					</h4>
					<p class="text-base" style="color: #4A4A4A;">
						Acompañamiento completo en procesos de adopción nacional e internacional
					</p>
				</div>

				<div class="text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
					<div class="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4" style="background-color: var(--primary-red);">
						<i class="fas fa-file-contract text-white text-2xl"></i>
					</div>
					<h4 class="text-lg font-semibold mb-2 uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
						Sucesiones
					</h4>
					<p class="text-base" style="color: #4A4A4A;">
						Gestión completa de trámites sucesorales y herencias
					</p>
				</div>
			</div>
		</div>
	</section>

	<script>
		// Form submission handling
		document.addEventListener('DOMContentLoaded', function() {
			const form = document.querySelector('.contact-form');
			
			form.addEventListener('submit', function(e) {
				e.preventDefault();
				
				// Here you would typically send the form data to your server
				// For now, we'll just show a success message
				alert('¡Gracias por tu consulta! Te contactaremos pronto.');
				
				// Reset form
				form.reset();
			});
		});
	</script>
</Layout>
