---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Preguntas Frecuentes - Familia y Ley Abogados">
	<!-- Hero Section -->
	<section class="relative h-[40vh] lg:h-[50vh] flex items-center bg-cover bg-center bg-no-repeat" style="background-image: url('/images/hero-bg3.jpg');">
		<!-- Black Overlay -->
		<div class="absolute inset-0" style="background-color: rgba(0, 0, 0, 0.6);"></div>
		<div class="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 w-full relative z-10">
			<div class="text-center">
				<h1 class="text-4xl sm:text-5xl lg:text-6xl font-medium text-white mb-4 leading-tight uppercase" style="font-family: '<PERSON><PERSON> Sans', sans-serif;">
					Preguntas Frecuentes
				</h1>
				<p class="text-lg lg:text-xl text-white/90 max-w-4xl mx-auto leading-relaxed">
					Resolvemos las dudas más comunes sobre nuestros servicios legales especializados
				</p>
			</div>
		</div>
	</section>

	<!-- FAQ Categories Section -->
	<section class="py-16 lg:py-24 bg-white">
		<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
			<!-- Category Navigation -->
			<div class="flex flex-wrap justify-center gap-4 mb-12">
				<button class="category-btn active px-6 py-3 font-medium transition-all duration-300" data-category="familia" style="background-color: var(--primary-red); color: white;">
					Derecho de Familia
				</button>
				<button class="category-btn px-6 py-3 font-medium transition-all duration-300" data-category="herencias" style="background-color: #F5F5F5; color: #314E52;">
					Herencia y Sucesiones
				</button>
				<button class="category-btn px-6 py-3 font-medium transition-all duration-300" data-category="civil" style="background-color: #F5F5F5; color: #314E52;">
					Derecho Civil
				</button>
			</div>

			<!-- Derecho de Familia FAQs -->
			<div id="familia" class="faq-category active">
				<h2 class="text-3xl sm:text-4xl lg:text-5xl font-medium mb-8 text-center uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
					Derecho de Familia
				</h2>
				
				<div class="divide-y" style="border-color: var(--primary-red);">
					<!-- FAQ Item 1 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Cómo se tramita una adopción en Colombia?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								El proceso de adopción en Colombia se realiza a través del ICBF (Instituto Colombiano de Bienestar Familiar).
								Incluye evaluación psicosocial, preparación de documentos, curso de preparación para padres adoptivos,
								asignación del menor y seguimiento post-adopción. El proceso puede tomar entre 12 a 24 meses dependiendo del caso.
							</p>
						</div>
					</div>

					<!-- FAQ Item 2 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Puedo divorciarme desde el exterior?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								Sí, es posible tramitar el divorcio desde el exterior. Podemos representarte mediante poder especial
								debidamente apostillado. El proceso puede ser notarial (mutuo acuerdo) o judicial (contencioso).
								Te asesoramos en todos los trámites consulares necesarios y la documentación requerida.
							</p>
						</div>
					</div>

					<!-- FAQ Item 3 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Es necesario abogado para alimentos?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								Aunque no es obligatorio, es altamente recomendable contar con representación legal en procesos de alimentos.
								Un abogado especializado puede ayudarte a calcular correctamente la cuota, presentar las pruebas necesarias
								y garantizar que se protejan los derechos del menor y del alimentante.
							</p>
						</div>
					</div>

					<!-- FAQ Item 4 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Qué documentos necesito para la custodia?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								Para solicitar custodia necesitas: registro civil del menor, cédulas de los padres, certificado de ingresos,
								certificado de vivienda, referencias personales, evaluación psicológica y social. También pueden requerirse
								otros documentos específicos según el caso particular.
							</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Herencia y Sucesiones FAQs -->
			<div id="herencias" class="faq-category hidden">
				<h2 class="text-3xl sm:text-4xl lg:text-5xl font-medium mb-8 text-center uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
					Herencia y Sucesiones
				</h2>
				
				<div class="divide-y" style="border-color: var(--primary-red);">
					<!-- FAQ Item 1 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Cuánto tarda una sucesión?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								El tiempo varía según la complejidad del caso. Una sucesión notarial (sin conflictos) puede tomar
								entre 2 a 4 meses. Una sucesión judicial puede extenderse de 6 meses a 2 años, dependiendo de
								la existencia de bienes en el exterior, conflictos entre herederos o complejidad patrimonial.
							</p>
						</div>
					</div>

					<!-- FAQ Item 2 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Puedo hacer testamento desde el exterior?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								Sí, puedes otorgar testamento desde el exterior ante el consulado colombiano correspondiente.
								El testamento debe cumplir con las formalidades legales colombianas y ser debidamente apostillado.
								Te asesoramos en todo el proceso y documentación necesaria.
							</p>
						</div>
					</div>

					<!-- FAQ Item 3 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Qué impuestos debo pagar en una herencia?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								En Colombia, las herencias están sujetas al impuesto de ganancia ocasional cuando superan ciertos montos.
								También pueden aplicar impuestos de registro y otros gravámenes municipales. Te asesoramos para
								optimizar la carga tributaria de manera legal.
							</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Derecho Civil FAQs -->
			<div id="civil" class="faq-category hidden">
				<h2 class="text-3xl sm:text-4xl lg:text-5xl font-medium mb-8 text-center uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
					Derecho Civil
				</h2>
				
				<div class="divide-y" style="border-color: var(--primary-red);">
					<!-- FAQ Item 1 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Qué es la responsabilidad civil?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								La responsabilidad civil es la obligación de reparar el daño causado a otra persona por acción u omisión.
								Puede ser contractual (incumplimiento de contrato) o extracontractual (daño sin relación contractual previa).
								Incluye daño emergente, lucro cesante y daño moral.
							</p>
						</div>
					</div>

					<!-- FAQ Item 2 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Cómo puedo reclamar daños y perjuicios?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								Para reclamar daños y perjuicios debes demostrar: el daño sufrido, la conducta del responsable,
								el nexo causal entre la conducta y el daño, y la culpa o dolo. Es importante actuar dentro de
								los términos de prescripción y contar con las pruebas necesarias.
							</p>
						</div>
					</div>

					<!-- FAQ Item 3 -->
					<div class="faq-item py-8">
						<button class="faq-button w-full text-left flex justify-between items-start group">
							<h3 class="text-xl font-medium pr-8 transition-colors uppercase" style="color: #314E52; font-family: 'Josefin Sans', sans-serif;">
								¿Qué validez tienen los contratos verbales?
							</h3>
							<div class="flex-shrink-0 ml-6">
								<span class="faq-icon text-3xl font-bold transition-colors" style="color: var(--primary-red);">+</span>
							</div>
						</button>
						<div class="faq-content mt-6 hidden">
							<p class="leading-relaxed text-lg pr-12" style="color: #868686;">
								Los contratos verbales tienen validez legal en Colombia, pero su prueba puede ser compleja.
								Para ciertos contratos (como compraventa de inmuebles) se requiere escritura pública.
								Recomendamos siempre documentar los acuerdos por escrito para evitar conflictos futuros.
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="py-16 lg:py-20" style="background-color: var(--primary-red);">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
			<h3 class="text-3xl sm:text-4xl lg:text-5xl font-medium mb-6 text-white uppercase" style="font-family: 'Josefin Sans', sans-serif;">
				¿No encontraste tu respuesta?
			</h3>
			<p class="text-lg lg:text-xl text-white/90 mb-8 leading-relaxed">
				Contáctanos para una consulta personalizada y resolvemos todas tus dudas legales
			</p>
			<a href="/contacto" class="inline-flex items-center px-8 py-4 text-lg font-medium text-white bg-white/20 hover:bg-white/30 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
				<i class="fas fa-phone mr-3"></i>
				Contactar Ahora
			</a>
		</div>
	</section>

	<script>
		// FAQ functionality
		document.addEventListener('DOMContentLoaded', function() {
			// Category switching
			const categoryBtns = document.querySelectorAll('.category-btn');
			const faqCategories = document.querySelectorAll('.faq-category');

			categoryBtns.forEach(btn => {
				btn.addEventListener('click', function() {
					const category = this.getAttribute('data-category');
					
					// Update button states
					categoryBtns.forEach(b => {
						b.classList.remove('active');
						b.style.backgroundColor = '#F5F5F5';
						b.style.color = '#314E52';
					});
					this.classList.add('active');
					this.style.backgroundColor = 'var(--primary-red)';
					this.style.color = 'white';
					
					// Update category visibility
					faqCategories.forEach(cat => {
						cat.classList.add('hidden');
						cat.classList.remove('active');
					});
					document.getElementById(category).classList.remove('hidden');
					document.getElementById(category).classList.add('active');
				});
			});

			// FAQ accordion functionality
			const faqButtons = document.querySelectorAll('.faq-button');
			
			faqButtons.forEach(button => {
				button.addEventListener('click', function() {
					const faqItem = this.closest('.faq-item');
					const content = faqItem.querySelector('.faq-content');
					const icon = faqItem.querySelector('.faq-icon');
					
					// Close all other FAQ items in the same category
					const activeCategory = document.querySelector('.faq-category.active');
					const allFaqItems = activeCategory.querySelectorAll('.faq-item');
					
					allFaqItems.forEach(item => {
						if (item !== faqItem) {
							item.querySelector('.faq-content').classList.add('hidden');
							item.querySelector('.faq-icon').textContent = '+';
						}
					});
					
					// Toggle current FAQ item
					if (content.classList.contains('hidden')) {
						content.classList.remove('hidden');
						icon.textContent = '−';
					} else {
						content.classList.add('hidden');
						icon.textContent = '+';
					}
				});
			});
		});
	</script>
</Layout>
